import * as THREE from 'three';
import {OrbitControls} from 'three/examples/jsm/controls/OrbitControls';

export interface SceneConfig {
	canvas: HTMLCanvasElement;
	alpha?: boolean;
	antialias?: boolean;
	powerPreference?: 'default' | 'high-performance' | 'low-power';
	enableOrbitControls?: boolean;
}

interface ViewportInfo {
	width: number;
	height: number;
	devicePixelRatio: number;
	isPortrait: boolean;
	isMobile: boolean;
}

export class SceneManager {
	public scene: THREE.Scene;
	public camera: THREE.PerspectiveCamera;
	public renderer: THREE.WebGLRenderer;
	public canvas: HTMLCanvasElement;
	public controls?: OrbitControls;
	private animationId: number | null = null;
	private resizeObserver: ResizeObserver | null = null;
	private isDestroyed = false;
	private resizeTimeout: number | null = null;
	private lastViewportInfo: ViewportInfo | null = null;
	private isPixel6Device = false;

	constructor(config: SceneConfig) {
		this.canvas = config.canvas;

		// Detect Pixel 6 device
		this.detectPixel6Device();

		// Initialize scene
		this.scene = new THREE.Scene();
		this.scene.background = new THREE.Color(0x222222);

		// Get initial viewport info
		const viewportInfo = this.getViewportInfo();

		// Initialize camera
		this.camera = new THREE.PerspectiveCamera(
			75,
			viewportInfo.width / viewportInfo.height,
			0.1,
			1000
		);

		// Initialize renderer with mobile-optimized settings
		this.renderer = new THREE.WebGLRenderer({
			canvas: this.canvas,
			alpha: config.alpha ?? true,
			antialias: this.shouldUseAntialias(config.antialias),
			powerPreference: this.getPowerPreference(config.powerPreference)
		});

		// Set initial size and pixel ratio
		this.updateRendererSize(viewportInfo);

		// Initialize OrbitControls if enabled
		if (config.enableOrbitControls ?? false) {
			this.controls = new OrbitControls(this.camera, this.renderer.domElement);

			// Configure OrbitControls
			this.controls.enableDamping = true;
			this.controls.dampingFactor = 0.05;
			this.controls.enableZoom = true;
			this.controls.enablePan = true;
			this.controls.enableRotate = true;

			console.log('OrbitControls initialized successfully');
		} else {
			console.log('OrbitControls disabled');
		}

		// Set up resize observer with mobile optimizations
		this.setupResizeObserver();
	}

	private detectPixel6Device(): void {
		const userAgent = navigator.userAgent.toLowerCase();
		const isAndroid = userAgent.includes('android');
		const isChrome = userAgent.includes('chrome');

		// Check for Pixel 6 specific characteristics
		if (isAndroid && isChrome) {
			// Pixel 6 has specific screen dimensions and device pixel ratio
			const screenWidth = screen.width;
			const screenHeight = screen.height;
			const dpr = window.devicePixelRatio;

			// Pixel 6: 1080x2400 with 2.625 DPR, Pixel 6 Pro: 1440x3120 with 3.5 DPR
			const isPixel6 = (screenWidth === 411 && screenHeight === 915 && Math.abs(dpr - 2.625) < 0.1) ||
							 (screenWidth === 412 && screenHeight === 915 && Math.abs(dpr - 2.625) < 0.1);
			const isPixel6Pro = (screenWidth === 411 && screenHeight === 891 && Math.abs(dpr - 3.5) < 0.1) ||
								(screenWidth === 412 && screenHeight === 892 && Math.abs(dpr - 3.5) < 0.1);

			this.isPixel6Device = isPixel6 || isPixel6Pro;

			if (this.isPixel6Device) {
				console.log('Pixel 6 device detected - applying optimizations');
			}
		}
	}

	private getViewportInfo(): ViewportInfo {
		// Use multiple methods to get accurate viewport dimensions
		const visualViewport = window.visualViewport;
		const documentElement = document.documentElement;

		let width: number;
		let height: number;

		if (visualViewport) {
			// Use Visual Viewport API when available (most accurate for mobile)
			width = visualViewport.width;
			height = visualViewport.height;
		} else {
			// Fallback to traditional methods
			width = Math.max(
				documentElement.clientWidth || 0,
				window.innerWidth || 0
			);
			height = Math.max(
				documentElement.clientHeight || 0,
				window.innerHeight || 0
			);
		}

		// Ensure canvas matches these dimensions
		this.canvas.style.width = `${width}px`;
		this.canvas.style.height = `${height}px`;

		const devicePixelRatio = this.getOptimalPixelRatio();
		const isPortrait = height > width;
		const isMobile = this.isMobileDevice();

		return {
			width,
			height,
			devicePixelRatio,
			isPortrait,
			isMobile
		};
	}

	private getOptimalPixelRatio(): number {
		const baseDPR = window.devicePixelRatio || 1;

		// Special handling for Pixel 6 to prevent flickering
		if (this.isPixel6Device) {
			// Cap pixel ratio to 2 for Pixel 6 to reduce GPU load and prevent flickering
			return Math.min(baseDPR, 2);
		}

		// For other devices, use standard capping
		return Math.min(baseDPR, 2);
	}

	private isMobileDevice(): boolean {
		return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
			   window.innerWidth <= 768;
	}

	private shouldUseAntialias(configAntialias?: boolean): boolean {
		// Disable antialiasing on mobile devices for better performance
		if (this.isMobileDevice()) {
			return false;
		}
		return configAntialias ?? true;
	}

	private getPowerPreference(configPowerPreference?: 'default' | 'high-performance' | 'low-power'): 'default' | 'high-performance' | 'low-power' {
		// Use low-power mode on Pixel 6 to prevent overheating and flickering
		if (this.isPixel6Device) {
			return 'low-power';
		}

		// Use low-power mode on mobile devices for better battery life
		if (this.isMobileDevice()) {
			return 'low-power';
		}

		return configPowerPreference ?? 'high-performance';
	}

	private updateRendererSize(viewportInfo: ViewportInfo): void {
		// Update renderer size with proper pixel ratio handling
		this.renderer.setSize(viewportInfo.width, viewportInfo.height, false);
		this.renderer.setPixelRatio(viewportInfo.devicePixelRatio);

		// Store current viewport info
		this.lastViewportInfo = viewportInfo;
	}

	private setupResizeObserver(): void {
		// Use debounced resize handling for better performance on mobile
		this.resizeObserver = new ResizeObserver((entries) => {
			this.debouncedHandleResize();
		});

		this.resizeObserver.observe(this.canvas);

		// Also listen to window resize and orientation change events
		window.addEventListener('resize', this.debouncedHandleResize.bind(this), { passive: true });
		window.addEventListener('orientationchange', this.handleOrientationChange.bind(this), { passive: true });

		// Listen to Visual Viewport API changes for mobile browsers
		if (window.visualViewport) {
			window.visualViewport.addEventListener('resize', this.debouncedHandleResize.bind(this), { passive: true });
		}
	}

	private debouncedHandleResize(): void {
		if (this.resizeTimeout) {
			clearTimeout(this.resizeTimeout);
		}

		// Use shorter debounce for Pixel 6 to reduce flickering
		const debounceTime = this.isPixel6Device ? 50 : 100;

		this.resizeTimeout = window.setTimeout(() => {
			this.handleResize();
		}, debounceTime);
	}

	private handleOrientationChange(): void {
		// Handle orientation change with a longer delay to ensure viewport is stable
		setTimeout(() => {
			this.handleResize();
		}, 300);
	}

	private handleResize(): void {
		if (this.isDestroyed) return;

		const newViewportInfo = this.getViewportInfo();

		// Only update if dimensions actually changed significantly
		if (this.lastViewportInfo &&
			Math.abs(newViewportInfo.width - this.lastViewportInfo.width) < 5 &&
			Math.abs(newViewportInfo.height - this.lastViewportInfo.height) < 5) {
			return;
		}

		// Update camera aspect ratio
		this.camera.aspect = newViewportInfo.width / newViewportInfo.height;
		this.camera.updateProjectionMatrix();

		// Update renderer size
		this.updateRendererSize(newViewportInfo);

		// Force a render to prevent visual glitches
		if (this.scene && this.camera) {
			this.renderer.render(this.scene, this.camera);
		}
	}

	public startAnimation(animateCallback: (time: number) => void): void {
		if (this.isDestroyed) return;
		
		const animate = (time: number) => {
			if (this.isDestroyed) return;

			animateCallback(time);
			this.renderer.render(this.scene, this.camera);
			this.animationId = requestAnimationFrame(animate);

			// Update controls if they exist
			if (this.controls) {
				this.controls.update();
			}
		};
		
		this.animationId = requestAnimationFrame(animate);
	}

	public stopAnimation(): void {
		if (this.animationId !== null) {
			cancelAnimationFrame(this.animationId);
			this.animationId = null;
		}
	}

	public addToScene(object: THREE.Object3D): void {
		this.scene.add(object);
	}

	public removeFromScene(object: THREE.Object3D): void {
		this.scene.remove(object);
	}

	public destroy(): void {
		this.isDestroyed = true;
		this.stopAnimation();

		// Clear resize timeout
		if (this.resizeTimeout) {
			clearTimeout(this.resizeTimeout);
			this.resizeTimeout = null;
		}

		// Remove event listeners
		window.removeEventListener('resize', this.debouncedHandleResize.bind(this));
		window.removeEventListener('orientationchange', this.handleOrientationChange.bind(this));

		if (window.visualViewport) {
			window.visualViewport.removeEventListener('resize', this.debouncedHandleResize.bind(this));
		}

		if (this.resizeObserver) {
			this.resizeObserver.disconnect();
			this.resizeObserver = null;
		}

		// Dispose of controls if they exist
		if (this.controls) {
			this.controls.dispose();
			this.controls = undefined;
		}

		// Dispose of Three.js resources
		this.scene.traverse((object) => {
			if (object instanceof THREE.Mesh) {
				object.geometry.dispose();
				if (Array.isArray(object.material)) {
					object.material.forEach(material => material.dispose());
				} else {
					object.material.dispose();
				}
			}
		});

		this.renderer.dispose();
	}
}
