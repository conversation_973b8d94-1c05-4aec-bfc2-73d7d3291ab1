/**
 * Mobile Canvas Utilities
 * Provides helper functions for handling canvas on mobile devices
 */

export interface MobileViewportInfo {
	width: number;
	height: number;
	devicePixelRatio: number;
	isPortrait: boolean;
	isMobile: boolean;
	isPixel6: boolean;
	hasNotch: boolean;
	safeAreaInsets: {
		top: number;
		bottom: number;
		left: number;
		right: number;
	};
}

/**
 * Detects if the current device is a Google Pixel 6 or Pixel 6 Pro
 */
export function detectPixel6Device(): boolean {
	const userAgent = navigator.userAgent.toLowerCase();
	const isAndroid = userAgent.includes('android');
	const isChrome = userAgent.includes('chrome');
	
	if (!isAndroid || !isChrome) return false;
	
	const screenWidth = screen.width;
	const screenHeight = screen.height;
	const dpr = window.devicePixelRatio;
	
	// Pixel 6: 1080x2400 with 2.625 DPR
	const isPixel6 = (screenWidth === 411 && screenHeight === 915 && Math.abs(dpr - 2.625) < 0.1) ||
					 (screenWidth === 412 && screenHeight === 915 && Math.abs(dpr - 2.625) < 0.1);
	
	// Pixel 6 Pro: 1440x3120 with 3.5 DPR
	const isPixel6Pro = (screenWidth === 411 && screenHeight === 891 && Math.abs(dpr - 3.5) < 0.1) ||
						(screenWidth === 412 && screenHeight === 892 && Math.abs(dpr - 3.5) < 0.1);
	
	return isPixel6 || isPixel6Pro;
}

/**
 * Gets comprehensive mobile viewport information
 */
export function getMobileViewportInfo(): MobileViewportInfo {
	const visualViewport = window.visualViewport;
	const documentElement = document.documentElement;
	
	let width: number;
	let height: number;
	
	if (visualViewport) {
		// Use Visual Viewport API when available (most accurate for mobile)
		width = visualViewport.width;
		height = visualViewport.height;
	} else {
		// Fallback to traditional methods
		width = Math.max(
			documentElement.clientWidth || 0,
			window.innerWidth || 0
		);
		height = Math.max(
			documentElement.clientHeight || 0,
			window.innerHeight || 0
		);
	}
	
	const devicePixelRatio = window.devicePixelRatio || 1;
	const isPortrait = height > width;
	const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
					 window.innerWidth <= 768;
	const isPixel6 = detectPixel6Device();
	
	// Detect devices with notches or safe areas
	const hasNotch = CSS.supports('padding-top: env(safe-area-inset-top)');
	
	// Get safe area insets
	const safeAreaInsets = {
		top: getSafeAreaInset('top'),
		bottom: getSafeAreaInset('bottom'),
		left: getSafeAreaInset('left'),
		right: getSafeAreaInset('right')
	};
	
	return {
		width,
		height,
		devicePixelRatio,
		isPortrait,
		isMobile,
		isPixel6,
		hasNotch,
		safeAreaInsets
	};
}

/**
 * Gets safe area inset value for a specific side
 */
function getSafeAreaInset(side: 'top' | 'bottom' | 'left' | 'right'): number {
	if (!CSS.supports('padding-top: env(safe-area-inset-top)')) {
		return 0;
	}
	
	// Create a temporary element to measure safe area insets
	const testElement = document.createElement('div');
	testElement.style.position = 'fixed';
	testElement.style.top = '0';
	testElement.style.left = '0';
	testElement.style.width = '1px';
	testElement.style.height = '1px';
	testElement.style.visibility = 'hidden';
	testElement.style.paddingTop = `env(safe-area-inset-${side})`;
	
	document.body.appendChild(testElement);
	const computedStyle = window.getComputedStyle(testElement);
	const paddingValue = computedStyle.paddingTop;
	document.body.removeChild(testElement);
	
	return parseInt(paddingValue) || 0;
}

/**
 * Optimizes canvas for mobile devices
 */
export function optimizeCanvasForMobile(canvas: HTMLCanvasElement): void {
	const viewportInfo = getMobileViewportInfo();
	
	// Set canvas dimensions to match viewport
	canvas.style.position = 'fixed';
	canvas.style.top = '0';
	canvas.style.left = '0';
	canvas.style.width = '100vw';
	canvas.style.height = '100vh';
	canvas.style.zIndex = '-10';
	
	// Prevent user interactions that might interfere
	canvas.style.userSelect = 'none';
	canvas.style.touchAction = 'none';
	canvas.style.webkitUserSelect = 'none';
	
	// GPU acceleration
	canvas.style.transform = 'translateZ(0)';
	canvas.style.willChange = 'transform';
	
	// Pixel 6 specific optimizations
	if (viewportInfo.isPixel6) {
		canvas.style.transform = 'translate3d(0, 0, 0)';
		canvas.style.backfaceVisibility = 'hidden';
		canvas.style.perspective = '1000px';
		canvas.style.imageRendering = 'optimizeSpeed';
	}
	
	// Handle safe areas on devices with notches
	if (viewportInfo.hasNotch) {
		canvas.style.top = `env(safe-area-inset-top, 0px)`;
		canvas.style.left = `env(safe-area-inset-left, 0px)`;
		canvas.style.width = `calc(100vw - env(safe-area-inset-left, 0px) - env(safe-area-inset-right, 0px))`;
		canvas.style.height = `calc(100vh - env(safe-area-inset-top, 0px) - env(safe-area-inset-bottom, 0px))`;
	}
}

/**
 * Gets optimal pixel ratio for mobile devices
 */
export function getOptimalPixelRatio(isPixel6: boolean = false): number {
	const baseDPR = window.devicePixelRatio || 1;
	
	// Special handling for Pixel 6 to prevent flickering
	if (isPixel6) {
		return Math.min(baseDPR, 2);
	}
	
	// For other mobile devices, cap at 2 for performance
	return Math.min(baseDPR, 2);
}

/**
 * Debounces a function call
 */
export function debounce<T extends (...args: any[]) => any>(
	func: T,
	wait: number
): (...args: Parameters<T>) => void {
	let timeout: number | null = null;
	
	return (...args: Parameters<T>) => {
		if (timeout) {
			clearTimeout(timeout);
		}
		
		timeout = window.setTimeout(() => {
			func(...args);
		}, wait);
	};
}

/**
 * Handles orientation change with proper delay
 */
export function handleOrientationChange(callback: () => void): void {
	// Wait for orientation change to complete
	setTimeout(callback, 300);
}

/**
 * Sets up mobile-optimized event listeners for canvas resize
 */
export function setupMobileResizeListeners(
	canvas: HTMLCanvasElement,
	callback: () => void,
	isPixel6: boolean = false
): () => void {
	const debounceTime = isPixel6 ? 50 : 100;
	const debouncedCallback = debounce(callback, debounceTime);
	
	// Window resize
	window.addEventListener('resize', debouncedCallback, { passive: true });
	
	// Orientation change
	const orientationCallback = () => handleOrientationChange(callback);
	window.addEventListener('orientationchange', orientationCallback, { passive: true });
	
	// Visual Viewport API (for mobile browsers)
	if (window.visualViewport) {
		window.visualViewport.addEventListener('resize', debouncedCallback, { passive: true });
	}
	
	// Return cleanup function
	return () => {
		window.removeEventListener('resize', debouncedCallback);
		window.removeEventListener('orientationchange', orientationCallback);
		
		if (window.visualViewport) {
			window.visualViewport.removeEventListener('resize', debouncedCallback);
		}
	};
}
