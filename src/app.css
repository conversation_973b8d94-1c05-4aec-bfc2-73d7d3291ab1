@import 'tailwindcss';
@plugin '@tailwindcss/forms';
@plugin '@tailwindcss/typography';

/* Mobile viewport fixes */
html, body {
	margin: 0;
	padding: 0;
	overflow-x: hidden;
	/* Prevent iOS Safari bounce effect */
	overscroll-behavior: none;
	/* Ensure full height on mobile */
	min-height: 100vh;
	min-height: 100dvh; /* Dynamic viewport height for modern browsers */
}

/* Canvas specific mobile optimizations */
canvas {
	/* Prevent canvas from being selectable */
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;

	/* Prevent touch actions that might interfere */
	touch-action: none;

	/* Ensure canvas covers full viewport */
	position: fixed !important;
	top: 0 !important;
	left: 0 !important;
	width: 100vw !important;
	height: 100vh !important;
	height: 100dvh !important; /* Dynamic viewport height */

	/* Prevent canvas from causing scrollbars */
	max-width: 100vw;
	max-height: 100vh;
	max-height: 100dvh;

	/* GPU acceleration */
	transform: translateZ(0);
	will-change: transform;
}

/* Mobile-specific optimizations */
@media (max-width: 768px) {
	/* Prevent zoom on input focus */
	input, select, textarea {
		font-size: 16px;
	}

	/* Ensure proper viewport handling */
	html {
		/* Prevent horizontal scrolling */
		overflow-x: hidden;
		/* Use safe area insets for devices with notches */
		padding-top: env(safe-area-inset-top);
		padding-bottom: env(safe-area-inset-bottom);
		padding-left: env(safe-area-inset-left);
		padding-right: env(safe-area-inset-right);
	}

	/* Canvas adjustments for mobile */
	canvas {
		/* Account for safe areas on mobile devices */
		top: env(safe-area-inset-top, 0) !important;
		left: env(safe-area-inset-left, 0) !important;
		width: calc(100vw - env(safe-area-inset-left, 0) - env(safe-area-inset-right, 0)) !important;
		height: calc(100vh - env(safe-area-inset-top, 0) - env(safe-area-inset-bottom, 0)) !important;
		height: calc(100dvh - env(safe-area-inset-top, 0) - env(safe-area-inset-bottom, 0)) !important;
	}
}

/* Pixel 6 specific optimizations */
@media screen and (device-width: 411px) and (device-height: 915px) and (-webkit-device-pixel-ratio: 2.625),
       screen and (device-width: 412px) and (device-height: 915px) and (-webkit-device-pixel-ratio: 2.625) {
	/* Pixel 6 specific canvas optimizations */
	canvas {
		/* Force hardware acceleration */
		transform: translate3d(0, 0, 0);
		backface-visibility: hidden;
		perspective: 1000px;

		/* Reduce potential for flickering */
		image-rendering: optimizeSpeed;
		image-rendering: -webkit-optimize-contrast;
		image-rendering: optimize-contrast;
	}
}

/* Pixel 6 Pro specific optimizations */
@media screen and (device-width: 411px) and (device-height: 891px) and (-webkit-device-pixel-ratio: 3.5),
       screen and (device-width: 412px) and (device-height: 892px) and (-webkit-device-pixel-ratio: 3.5) {
	/* Pixel 6 Pro specific canvas optimizations */
	canvas {
		/* Force hardware acceleration */
		transform: translate3d(0, 0, 0);
		backface-visibility: hidden;
		perspective: 1000px;

		/* Reduce potential for flickering */
		image-rendering: optimizeSpeed;
		image-rendering: -webkit-optimize-contrast;
		image-rendering: optimize-contrast;
	}
}
